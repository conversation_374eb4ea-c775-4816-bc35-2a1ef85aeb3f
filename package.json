{"name": "modern-todo-app", "version": "1.0.0", "description": "Modern todo application with MotherDuck DuckDB backend, Frutiger Aero UI, and MCP integration", "main": "dist/main/index.js", "homepage": "./", "directories": {"doc": "docs"}, "scripts": {"build": "npm run build:main && npm run build:renderer", "build:main": "tsc -p tsconfig.main.json && tsc-alias -p tsconfig.main.json", "build:renderer": "vite build", "build:prod": "node build/build-prod.js", "build:prod:main": "NODE_ENV=production tsc -p tsconfig.main.json && tsc-alias -p tsconfig.main.json", "build:prod:renderer": "NODE_ENV=production vite build --config vite.config.prod.ts", "dev": "concurrently \"npm run dev:main\" \"npm run dev:renderer\"", "dev:main": "nodemon --exec \"ts-node -r tsconfig-paths/register src/main/index.ts\"", "dev:renderer": "vite", "electron": "electron .", "electron:dev": "concurrently \"npm run dev:renderer\" \"wait-on http://localhost:5173 && electron .\"", "start": "npm run build && electron .", "start:prod": "npm run build:prod && electron .", "test": "jest", "test:watch": "jest --watch", "test:prod": "NODE_ENV=production jest --passWithNoTests --watchAll=false", "lint": "eslint src/**/*.{ts,tsx} --fix", "lint:prod": "eslint src/**/*.{ts,tsx} --max-warnings 0", "format": "prettier --write src/**/*.{ts,tsx,css,json}", "analyze": "node build/analyze-bundle.js", "validate": "node build/validate-build.js", "security:harden": "node build/security-hardening.js", "security:validate": "node build/security-validation.js", "security:full": "npm run security:harden && npm run security:validate", "deploy": "node build/deploy.js", "deploy:prod": "npm run build:prod && npm run analyze && npm run validate && npm run security:full && npm run deploy", "preview": "vite preview --config vite.config.prod.ts", "db:init": "ts-node -r tsconfig-paths/register src/main/database/init.ts", "db:test": "ts-node -r tsconfig-paths/register src/main/database/test-connection.ts", "db:test-crud": "ts-node -r tsconfig-paths/register src/main/database/test-crud.ts", "db:test-params": "ts-node -r tsconfig-paths/register src/main/database/test-params.ts", "db:migrate": "ts-node -r tsconfig-paths/register src/main/database/migrate.ts", "db:seed": "ts-node -r tsconfig-paths/register src/main/database/seed.ts", "mcp:test": "ts-node -r tsconfig-paths/register src/main/mcp/test-connection.ts", "mcp:test-real": "ts-node -r tsconfig-paths/register src/main/mcp/test-real-integration.ts", "db:test-motherduck": "ts-node -r tsconfig-paths/register src/main/database/test-motherduck.ts", "db:examples-motherduck": "ts-node -r tsconfig-paths/register src/main/database/motherduck-data-examples.ts", "db:simple-examples": "ts-node -r tsconfig-paths/register src/main/database/simple-motherduck-examples.ts", "db:test-auto-init": "ts-node -r tsconfig-paths/register src/main/database/test-auto-initialization.ts", "package": "electron-builder", "package:all": "electron-builder -mwl", "package:prod": "npm run build:prod && electron-builder", "clean": "rm -rf dist release node_modules/.cache", "clean:all": "npm run clean && rm -rf node_modules"}, "keywords": ["todo", "electron", "react", "typescript", "duckdb", "motherduck", "mcp", "frutiger-aero", "glassmorphism", "offline-first"], "author": "Modern Todo Team", "license": "MIT", "type": "commonjs", "dependencies": {"@modelcontextprotocol/sdk": "^1.17.3", "@types/lunr": "^2.3.7", "@types/validator": "^13.15.2", "bcrypt": "^6.0.0", "crypto-js": "^4.2.0", "duckdb": "^1.3.2", "electron-log": "^5.0.1", "electron-store": "^10.0.0", "electron-updater": "^6.1.7", "immer": "^10.1.1", "lunr": "^2.3.9", "uuid": "^11.1.0", "validator": "^13.15.15", "zod": "^3.25.76"}, "devDependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@eslint/js": "^9.9.0", "@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/bcrypt": "^6.0.0", "@types/jest": "^29.5.12", "@types/node": "^24.3.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.0.1", "@typescript-eslint/parser": "^8.0.1", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "date-fns": "^3.6.0", "dotenv": "^17.2.1", "electron": "^32.0.1", "electron-builder": "^24.13.3", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "framer-motion": "^11.3.24", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lucide-react": "^0.427.0", "nodemon": "^3.1.10", "postcss": "^8.4.41", "prettier": "^3.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.2", "tailwindcss": "^3.4.9", "ts-jest": "^29.2.4", "ts-node": "^10.9.2", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "typescript": "^5.5.4", "vite": "^5.4.1", "wait-on": "^7.2.0", "zustand": "^4.5.4"}, "build": {"appId": "com.moderntodo.app", "productName": "Modern Todo", "directories": {"output": "release", "buildResources": "build/resources"}, "files": ["dist/**/*", "assets/**/*", "package.json", "!**/*.map", "!**/*.ts", "!src/**/*", "!build/**/*", "!docs/**/*", "!*.md", "!.env*", "!jest.config.js", "!tsconfig*.json", "!vite.config*.ts", "!postcss.config.js", "!tailwind.config.js"], "extraResources": [{"from": "assets", "to": "assets", "filter": ["**/*"]}], "compression": "maximum", "removePackageScripts": true, "nodeGypRebuild": false, "buildDependenciesFromSource": false, "mac": {"category": "public.app-category.productivity", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "build/resources/icon.icns", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64"]}], "icon": "build/resources/icon.ico", "publisherName": "Modern Todo Team", "verifyUpdateCodeSignature": false}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "icon": "build/resources/icon.png", "category": "Office", "maintainer": "Modern Todo Team", "vendor": "Modern Todo Team"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Modern Todo"}, "dmg": {"title": "Modern Todo ${version}", "icon": "build/resources/icon.icns", "background": "build/resources/dmg-background.png", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "publish": {"provider": "generic", "url": "https://releases.moderntodo.app/"}}}