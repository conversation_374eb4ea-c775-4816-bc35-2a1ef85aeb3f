import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../../providers/ThemeProvider';
import { useAuth } from '../../contexts/AuthContext';
import { Sun, Moon, Menu, X, User, Search, BarChart3, Settings } from 'lucide-react';
import { UserProfile } from '../auth/UserProfile';
import { AnalyticsDashboard } from '../analytics/AnalyticsDashboard';
import { AccessibilityFeedback } from '../ui/AccessibilityFeedback';
import { KeyboardShortcutsHelp } from '../ui/KeyboardShortcutsHelp';

interface TitleBarProps {
  systemInfo?: any;
  onToggleTheme: () => void;
  theme: 'light' | 'dark';
  onToggleSidebar?: () => void;
  sidebarOpen?: boolean;
  isMobile?: boolean;
  todos?: any[];
}

export const TitleBar: React.FC<TitleBarProps> = ({
  systemInfo,
  onToggleTheme,
  theme,
  onToggleSidebar,
  sidebarOpen,
  isMobile,
  todos = []
}) => {
  const { user, logout } = useAuth();
  const [showProfile, setShowProfile] = useState(false);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [showAccessibility, setShowAccessibility] = useState(false);
  const [showKeyboardHelp, setShowKeyboardHelp] = useState(false);

  return (
    <>
      <div className="w-full h-12 flex items-center justify-between px-4 bg-fa-white-glass backdrop-blur-xl border-b border-fa-white-frosted">
        <div className="flex items-center space-x-3">
          {/* Mobile menu toggle - only show on mobile when sidebar toggle is available */}
          {isMobile && onToggleSidebar && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 rounded-lg fa-hover-lift fa-touch-target"
              onClick={onToggleSidebar}
            >
              {sidebarOpen ? (
                <X className="w-5 h-5 text-fa-gray-600" />
              ) : (
                <Menu className="w-5 h-5 text-fa-gray-600" />
              )}
            </motion.button>
          )}

          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="p-2 rounded-lg fa-hover-lift fa-touch-target"
            onClick={onToggleTheme}
          >
            {theme === 'light' ? (
              <Sun className="w-5 h-5 text-fa-blue-600" />
            ) : (
              <Moon className="w-5 h-5 text-fa-aqua-400" />
            )}
          </motion.button>

          <h1 className={`fa-heading-3 font-bold text-fa-gray-800 ${isMobile ? 'text-lg' : ''}`}>
            Modern Todo
          </h1>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Search Button */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="p-2 rounded-lg fa-hover-lift fa-touch-target"
            data-tour="search"
            title="Search todos"
          >
            <Search className="w-5 h-5 text-fa-gray-600" />
          </motion.button>

          {/* Analytics Button */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="p-2 rounded-lg fa-hover-lift fa-touch-target"
            onClick={() => setShowAnalytics(true)}
            data-tour="analytics"
            title="View analytics"
          >
            <BarChart3 className="w-5 h-5 text-fa-gray-600" />
          </motion.button>

          {/* Accessibility Button */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="p-2 rounded-lg fa-hover-lift fa-touch-target"
            onClick={() => setShowAccessibility(true)}
            data-tour="accessibility"
            title="Accessibility settings"
          >
            <Settings className="w-5 h-5 text-fa-gray-600" />
          </motion.button>

          {user && (
            <div className="flex items-center space-x-2">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-2 rounded-lg fa-hover-lift flex items-center text-fa-gray-700"
                onClick={() => setShowProfile(true)}
              >
                <User className="w-5 h-5" />
                <span className="ml-2 text-sm font-medium hidden md:inline">
                  {user.username}
                </span>
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-2 rounded-lg fa-hover-lift text-fa-gray-700"
                onClick={logout}
              >
                <span className="text-sm font-medium">Logout</span>
              </motion.button>
            </div>
          )}
          
          <div className="text-xs text-fa-gray-500">
            {systemInfo?.platform} • {systemInfo?.version}
          </div>
        </div>
      </div>

      {/* Modals */}
      {showProfile && <UserProfile onClose={() => setShowProfile(false)} />}

      {showAnalytics && (
        <AnalyticsDashboard
          isOpen={showAnalytics}
          onClose={() => setShowAnalytics(false)}
          todos={todos}
        />
      )}

      {showAccessibility && (
        <AccessibilityFeedback
          isOpen={showAccessibility}
          onClose={() => setShowAccessibility(false)}
        />
      )}

      {showKeyboardHelp && (
        <KeyboardShortcutsHelp
          isOpen={showKeyboardHelp}
          onClose={() => setShowKeyboardHelp(false)}
        />
      )}
    </>
  );
};