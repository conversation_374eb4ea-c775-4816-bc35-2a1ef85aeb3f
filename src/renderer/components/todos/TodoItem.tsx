import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Check,
  Flag,
  Calendar,
  Edit3,
  Trash2,
  AlertCircle,
  AlertTriangle,
  AlertOctagon,
  Tag,
  Folder
} from 'lucide-react';
import { Todo } from '@shared/types';
import { todoService } from '@renderer/services/todo.service';
import { ConfirmationDialog } from '@renderer/components/ui/ConfirmationDialog';
import { TodoForm } from './TodoForm';
import { useTodoStore } from '@renderer/stores/todoStore';
import { PriorityIndicator } from '@renderer/components/ui/PriorityIndicator';
import {
  isHighPriority,
  isVeryHighPriority,
  getPriorityBarHeight,
  getPriorityGradient
} from '@renderer/utils/priorityUtils';
import {
  getOverdueInfo,
  getOverdueClasses,
  getOverdueIcon
} from '@renderer/utils/overdueUtils';
import {
  cardHover,
  buttonHover,
  loadingSpinner,
  smoothTransition,
  fastTransition
} from '@renderer/utils/animations';
import { useHoverAnimation, useLoadingAnimation } from '@renderer/hooks/useAnimations';
import { useTodoAnnouncements } from '@renderer/hooks/useScreenReader';

interface TodoItemProps {
  todo: Todo;
  onUpdate?: (updatedTodo: Todo) => void;
  onDelete?: (todoId: string) => void;
}

export const TodoItem: React.FC<TodoItemProps> = ({ todo, onUpdate, onDelete }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(todo.title);
  const [isLoading, setIsLoading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);

  const { categories } = useTodoStore();
  const category = categories.find(cat => cat.id === todo.category_id);

  // Enhanced animation hooks
  const { hoverProps, isHovered } = useHoverAnimation();
  const { spinnerVariants } = useLoadingAnimation();

  // Accessibility announcements
  const {
    announceTodoCompleted,
    announceTodoIncomplete,
    announceTodoUpdated,
    announceTodoDeleted,
    announceError
  } = useTodoAnnouncements();

  const isCompleted = todo.status === 'completed';
  const overdueInfo = getOverdueInfo(todo);
  const isOverdue = overdueInfo.isOverdue;
  const overdueClasses = getOverdueClasses(overdueInfo.severity);

  const handleToggleComplete = useCallback(async () => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      const updatedTodo = await todoService.toggleTodoCompletion(todo.id, todo.status);
      onUpdate?.(updatedTodo);

      // Announce the status change
      if (updatedTodo.status === 'completed') {
        announceTodoCompleted(todo.title);
      } else {
        announceTodoIncomplete(todo.title);
      }
    } catch (error) {
      console.error('Failed to toggle todo completion:', error);
      announceError('Failed to update todo status');
    } finally {
      setIsLoading(false);
    }
  }, [todo.id, todo.status, todo.title, isLoading, onUpdate, announceTodoCompleted, announceTodoIncomplete, announceError]);

  const handleDelete = useCallback(async () => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      await todoService.deleteTodo(todo.id);
      onDelete?.(todo.id);
      setShowDeleteDialog(false);
      announceTodoDeleted(todo.title);
    } catch (error) {
      console.error('Failed to delete todo:', error);
      announceError('Failed to delete todo');
    } finally {
      setIsLoading(false);
    }
  }, [todo.id, todo.title, isLoading, onDelete, announceTodoDeleted, announceError]);

  const handleEdit = () => {
    setShowEditForm(true);
  };

  const handleFormEdit = (updatedTodo: Todo) => {
    onUpdate?.(updatedTodo);
    setShowEditForm(false);
  };

  const handleSaveEdit = useCallback(async () => {
    if (isLoading || editValue.trim() === todo.title) {
      setIsEditing(false);
      return;
    }

    setIsLoading(true);
    try {
      const updatedTodo = await todoService.updateTodo(todo.id, { title: editValue.trim() });
      onUpdate?.(updatedTodo);
      setIsEditing(false);
      announceTodoUpdated(editValue.trim());
    } catch (error) {
      console.error('Failed to update todo:', error);
      announceError('Failed to update todo');
      setEditValue(todo.title); // Reset to original value
    } finally {
      setIsLoading(false);
    }
  }, [todo.id, todo.title, editValue, isLoading, onUpdate, announceTodoUpdated, announceError]);

  const handleCancelEdit = () => {
    setEditValue(todo.title);
    setIsEditing(false);
  };

  // Remove old priority functions - now using PriorityIndicator component

  const getStatusColor = () => {
    switch (todo.status) {
      case 'completed': return 'text-fa-success';
      case 'in_progress': return 'text-fa-info';
      case 'pending': return 'text-fa-gray-500';
      case 'cancelled': return 'text-fa-error';
      case 'archived': return 'text-fa-gray-400';
      default: return 'text-fa-gray-500';
    }
  };

  return (
    <>
      <motion.div
        className={`fa-todo-card relative ${
          isCompleted ? 'opacity-70' : ''
        } ${isOverdue ? `${overdueClasses.borderClass} ${overdueClasses.bgClass} ${overdueClasses.pulseClass}` : ''} ${
          isVeryHighPriority(todo.priority) && !isOverdue ? 'ring-2 ring-red-500/30 shadow-lg shadow-red-500/20' : ''
        } ${
          isHighPriority(todo.priority) && !isVeryHighPriority(todo.priority) && !isOverdue ? 'ring-1 ring-orange-500/20' : ''
        }`}
        variants={cardHover}
        initial="initial"
        whileHover="hover"
        whileTap="tap"
        transition={smoothTransition}
        {...hoverProps}
        role="article"
        aria-labelledby={`todo-title-${todo.id}`}
        aria-describedby={`todo-details-${todo.id}`}
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleToggleComplete();
          }
          if (e.key === 'e' || e.key === 'E') {
            e.preventDefault();
            handleEdit();
          }
          if (e.key === 'Delete' || e.key === 'Backspace') {
            e.preventDefault();
            setShowDeleteDialog(true);
          }
        }}
      >
        <div className="flex items-start">
          {/* Checkbox */}
          <motion.button
            variants={buttonHover}
            initial="initial"
            whileHover="hover"
            whileTap="tap"
            onClick={handleToggleComplete}
            disabled={isLoading}
            className={`fa-touch-target flex-shrink-0 w-6 h-6 rounded-full border-2 flex items-center justify-center mr-4 mt-1 fa-transition-all ${
              isCompleted
                ? 'bg-fa-success border-fa-success text-white'
                : 'border-fa-gray-300 hover:border-fa-blue-400'
            } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
            role="checkbox"
            aria-checked={isCompleted}
            aria-label={`Mark "${todo.title}" as ${isCompleted ? 'incomplete' : 'complete'}`}
            aria-describedby={`todo-status-${todo.id}`}
          >
            <AnimatePresence mode="wait">
              {isLoading ? (
                <motion.div
                  key="loading"
                  variants={spinnerVariants}
                  animate="animate"
                  className="w-3 h-3 border border-current border-t-transparent rounded-full"
                />
              ) : (
                isCompleted && (
                  <motion.div
                    key="check"
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0, opacity: 0 }}
                    transition={fastTransition}
                  >
                    <Check className="w-4 h-4" />
                  </motion.div>
                )
              )}
            </AnimatePresence>
          </motion.button>

          {/* Hidden status for screen readers */}
          <div id={`todo-status-${todo.id}`} className="sr-only">
            This todo is currently {isCompleted ? 'completed' : 'incomplete'}.
          </div>

        {/* Todo Content */}
        <div className="flex-1 min-w-0">
          {isEditing ? (
            <div className="space-y-2">
              <input
                type="text"
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleSaveEdit();
                  if (e.key === 'Escape') handleCancelEdit();
                }}
                className="w-full bg-transparent text-fa-gray-800 focus:outline-none border-b border-fa-blue-300 pb-1 focus:ring-2 focus:ring-fa-blue-400 focus:ring-opacity-50"
                autoFocus
                disabled={isLoading}
                aria-label="Edit todo title"
                aria-describedby={`edit-help-${todo.id}`}
              />
              <div id={`edit-help-${todo.id}`} className="sr-only">
                Press Enter to save, Escape to cancel
              </div>
              <div className="flex items-center space-x-2">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleSaveEdit}
                  disabled={isLoading}
                  className="fa-button-glass text-xs px-2 py-1"
                  aria-label="Save changes to todo"
                >
                  Save
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleCancelEdit}
                  disabled={isLoading}
                  className="fa-button-glass text-xs px-2 py-1"
                  aria-label="Cancel editing and discard changes"
                >
                  Cancel
                </motion.button>
              </div>
            </div>
          ) : (
            <>
              <div className="flex items-start justify-between">
                <h3
                  id={`todo-title-${todo.id}`}
                  className={`text-lg font-medium flex-1 ${
                    isCompleted
                      ? 'line-through text-fa-gray-500'
                      : 'text-fa-gray-800'
                  }`}
                >
                  {todo.title}
                </h3>
                {isOverdue && (
                  <div
                    className={`flex items-center ml-2 ${overdueClasses.textClass}`}
                    role="alert"
                    aria-label={`Overdue by ${overdueInfo.formattedDuration}`}
                  >
                    {overdueInfo.severity === 'slight' && <AlertCircle className={`w-4 h-4 mr-1 ${overdueClasses.iconClass}`} aria-hidden="true" />}
                    {overdueInfo.severity === 'moderate' && <AlertTriangle className={`w-4 h-4 mr-1 ${overdueClasses.iconClass}`} aria-hidden="true" />}
                    {overdueInfo.severity === 'severe' && <AlertOctagon className={`w-4 h-4 mr-1 ${overdueClasses.iconClass} ${overdueClasses.pulseClass}`} aria-hidden="true" />}
                    <span className="fa-caption font-medium">{overdueInfo.formattedDuration}</span>
                  </div>
                )}
              </div>

              {todo.description && (
                <p
                  className="fa-body text-fa-gray-600 mt-1 line-clamp-2"
                  id={`todo-description-${todo.id}`}
                >
                  {todo.description}
                </p>
              )}

              {/* Hidden details for screen readers */}
              <div
                id={`todo-details-${todo.id}`}
                className="sr-only"
              >
                Status: {todo.status?.replace('_', ' ') || 'pending'}.
                {category && ` Category: ${category.name}.`}
                {todo.tags && todo.tags.length > 0 && ` Tags: ${todo.tags.join(', ')}.`}
                {todo.due_date && ` Due: ${todoService.formatDueDate(todo.due_date)}.`}
                {isOverdue && ` This task is overdue by ${overdueInfo.formattedDuration}.`}
                Priority: {todo.priority}.
                {todo.description && ` Description: ${todo.description}`}
              </div>

              <div
                className="flex items-center space-x-4 mt-2 flex-wrap gap-2"
                aria-hidden="true"
              >
                {/* Status Badge */}
                <span className={`fa-caption px-2 py-1 rounded-full bg-fa-white-glass ${getStatusColor()}`}>
                  {todo.status?.replace('_', ' ') || 'pending'}
                </span>

                {/* Category */}
                {category && (
                  <div className="flex items-center space-x-1">
                    <div
                      className="w-3 h-3 rounded"
                      style={{ backgroundColor: category.color }}
                      aria-label={`Category color: ${category.color}`}
                    />
                    <span className="fa-caption text-fa-gray-600">
                      {category.name}
                    </span>
                  </div>
                )}

                {/* Tags */}
                {todo.tags && todo.tags.length > 0 && (
                  <div className="flex items-center space-x-1">
                    <Tag className="w-3 h-3 text-fa-gray-400" aria-hidden="true" />
                    {todo.tags.slice(0, 3).map((tag, index) => (
                      <span key={index} className="fa-caption bg-fa-blue-100 text-fa-blue-700 px-2 py-1 rounded-full">
                        {tag}
                      </span>
                    ))}
                    {todo.tags.length > 3 && (
                      <span className="fa-caption text-fa-gray-400">+{todo.tags.length - 3}</span>
                    )}
                  </div>
                )}

                {/* Due Date */}
                {todo.due_date && (
                  <div className={`flex items-center ${isOverdue ? overdueClasses.textClass : 'text-fa-gray-500'}`}>
                    <Calendar className="w-4 h-4 mr-1" aria-hidden="true" />
                    <span className="fa-caption">{todoService.formatDueDate(todo.due_date)}</span>
                  </div>
                )}

                {/* Priority */}
                <PriorityIndicator
                  priority={todo.priority}
                  variant="badge"
                  size="sm"
                  showLabel={false}
                  animated={true}
                />
              </div>
            </>
          )}
        </div>

        {/* Action Buttons */}
        {!isEditing && (
          <motion.div
            className="flex items-center space-x-1 ml-2"
            initial={{ opacity: 0, width: 0, x: 10 }}
            animate={{
              opacity: isHovered ? 1 : 0,
              width: isHovered ? 'auto' : 0,
              x: isHovered ? 0 : 10
            }}
            transition={smoothTransition}
          >
            <motion.button
              variants={buttonHover}
              whileHover="hover"
              whileTap="tap"
              onClick={handleEdit}
              disabled={isLoading}
              className="fa-touch-target p-1 text-fa-gray-400 hover:text-fa-blue-500 disabled:opacity-50 fa-transition-all"
              aria-label={`Edit todo: ${todo.title}`}
              title="Edit todo"
            >
              <Edit3 className="w-4 h-4" aria-hidden="true" />
            </motion.button>

            <motion.button
              variants={buttonHover}
              whileHover="hover"
              whileTap="tap"
              onClick={() => setShowDeleteDialog(true)}
              disabled={isLoading}
              className="fa-touch-target p-1 text-fa-gray-400 hover:text-fa-error disabled:opacity-50 fa-transition-all"
              aria-label={`Delete todo: ${todo.title}`}
              title="Delete todo"
            >
              <Trash2 className="w-4 h-4" aria-hidden="true" />
            </motion.button>
          </motion.div>
        )}
      </motion.div>
        </div>

      {/* Enhanced Priority indicator bar */}
      <AnimatePresence>
        {isHighPriority(todo.priority) && (
          <motion.div
            initial={{ scaleY: 0, opacity: 0 }}
            animate={{ scaleY: 1, opacity: 1 }}
            exit={{ scaleY: 0, opacity: 0 }}
            transition={smoothTransition}
            className="absolute top-0 left-0 w-full rounded-t-2xl"
            style={{ transformOrigin: 'top' }}
          >
            <PriorityIndicator
              priority={todo.priority}
              variant="bar"
              animated={true}
              className="rounded-t-2xl"
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Additional glow effect for very high priority */}
      <AnimatePresence>
        {isVeryHighPriority(todo.priority) && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={smoothTransition}
            className="absolute -top-1 -left-1 -right-1 -bottom-1 rounded-2xl bg-gradient-to-r from-red-500/20 to-red-600/20 blur-sm -z-10 fa-glow-pulse"
          />
        )}
      </AnimatePresence>
    </motion.div>

    {/* Delete Confirmation Dialog */}
    <ConfirmationDialog
      isOpen={showDeleteDialog}
      onClose={() => setShowDeleteDialog(false)}
      onConfirm={handleDelete}
      title="Delete Todo"
      message={`Are you sure you want to delete "${todo.title}"? This action cannot be undone.`}
      confirmText="Delete"
      cancelText="Cancel"
      variant="danger"
      isLoading={isLoading}
    />

    {/* Edit Form Modal */}
    <TodoForm
      isOpen={showEditForm}
      onClose={() => setShowEditForm(false)}
      onSubmit={handleFormEdit}
      todo={todo}
      mode="edit"
    />
  </>
  );
};