import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>rk<PERSON>, 
  ArrowRight, 
  X, 
  CheckCircle, 
  Play,
  SkipForward,
  Settings,
  HelpCircle
} from 'lucide-react';
import { UserGuide } from '@renderer/components/ui/NotificationSystem';
import { onboardingService, OnboardingSettings } from '@renderer/services/onboarding.service';
import { useUIStore } from '@renderer/stores/uiStore';

interface OnboardingFlowProps {
  className?: string;
}

export const OnboardingFlow: React.FC<OnboardingFlowProps> = ({ className = '' }) => {
  const [showWelcome, setShowWelcome] = useState(false);
  const [showTour, setShowTour] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [settings, setSettings] = useState<OnboardingSettings>(onboardingService.getSettings());
  
  const { addNotification } = useUIStore();

  useEffect(() => {
    // Check if we should show welcome or auto-start tour
    if (onboardingService.shouldShowWelcome()) {
      setShowWelcome(true);
    } else if (onboardingService.shouldAutoStartTour()) {
      setShowTour(true);
    }
  }, []);

  const handleStartTour = () => {
    setShowWelcome(false);
    setShowTour(true);
  };

  const handleSkipWelcome = () => {
    setShowWelcome(false);
    onboardingService.markTourCompleted();
    addNotification({
      type: 'info',
      title: 'Welcome skipped',
      message: 'You can always start the tour later from the help menu.',
    });
  };

  const handleTourComplete = () => {
    setShowTour(false);
    onboardingService.markTourCompleted();
    addNotification({
      type: 'success',
      title: 'Tour completed!',
      message: 'You\'re all set to boost your productivity with Modern Todo.',
    });
  };

  const handleTourSkip = () => {
    setShowTour(false);
    onboardingService.markTourCompleted();
    addNotification({
      type: 'info',
      title: 'Tour skipped',
      message: 'You can restart the tour anytime from the help menu.',
    });
  };

  const handleSettingsUpdate = (newSettings: Partial<OnboardingSettings>) => {
    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);
    onboardingService.updateSettings(updatedSettings);
  };

  const handleRestartTour = () => {
    onboardingService.resetOnboarding();
    setShowWelcome(true);
  };

  return (
    <div className={className}>
      {/* Welcome Modal */}
      <AnimatePresence>
        {showWelcome && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="fa-glass-panel-frosted border border-fa-gray-200 rounded-xl shadow-2xl max-w-lg w-full p-8 text-center"
            >
              {/* Welcome Icon */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
                className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-fa-blue-500 to-fa-aqua-500 rounded-full flex items-center justify-center"
              >
                <Sparkles className="w-10 h-10 text-white" />
              </motion.div>

              {/* Welcome Content */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <h1 className="fa-heading-1 mb-4 bg-gradient-to-r from-fa-blue-600 to-fa-aqua-600 bg-clip-text text-transparent">
                  Welcome to Modern Todo!
                </h1>
                <p className="fa-body text-fa-gray-600 mb-8 leading-relaxed">
                  Your new productivity companion is ready! We've built an amazing experience 
                  with powerful features, beautiful design, and seamless cloud sync.
                </p>

                {/* Features Preview */}
                <div className="grid grid-cols-2 gap-4 mb-8 text-sm">
                  <div className="flex items-center space-x-2 text-fa-gray-700">
                    <CheckCircle className="w-4 h-4 text-fa-green-500" />
                    <span>Smart Search</span>
                  </div>
                  <div className="flex items-center space-x-2 text-fa-gray-700">
                    <CheckCircle className="w-4 h-4 text-fa-green-500" />
                    <span>Cloud Sync</span>
                  </div>
                  <div className="flex items-center space-x-2 text-fa-gray-700">
                    <CheckCircle className="w-4 h-4 text-fa-green-500" />
                    <span>Analytics</span>
                  </div>
                  <div className="flex items-center space-x-2 text-fa-gray-700">
                    <CheckCircle className="w-4 h-4 text-fa-green-500" />
                    <span>Accessibility</span>
                  </div>
                </div>
              </motion.div>

              {/* Action Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="flex flex-col sm:flex-row gap-3 justify-center"
              >
                <button
                  onClick={handleStartTour}
                  className="fa-button-gradient px-6 py-3 rounded-lg font-medium flex items-center justify-center space-x-2 group"
                >
                  <Play className="w-4 h-4 group-hover:scale-110 transition-transform" />
                  <span>Start Tour</span>
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </button>
                
                <div className="flex gap-2">
                  <button
                    onClick={() => setShowSettings(true)}
                    className="fa-button-ghost px-4 py-3 rounded-lg font-medium flex items-center space-x-2"
                  >
                    <Settings className="w-4 h-4" />
                    <span>Settings</span>
                  </button>
                  
                  <button
                    onClick={handleSkipWelcome}
                    className="fa-button-ghost px-4 py-3 rounded-lg font-medium flex items-center space-x-2"
                  >
                    <SkipForward className="w-4 h-4" />
                    <span>Skip</span>
                  </button>
                </div>
              </motion.div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Onboarding Settings Modal */}
      <AnimatePresence>
        {showSettings && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="fa-glass-panel-frosted border border-fa-gray-200 rounded-xl shadow-2xl max-w-md w-full p-6"
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="fa-heading-2 flex items-center space-x-2">
                  <Settings className="w-5 h-5 text-fa-blue-500" />
                  <span>Onboarding Settings</span>
                </h2>
                <button
                  onClick={() => setShowSettings(false)}
                  className="p-2 hover:bg-fa-gray-100 rounded-lg transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>

              <div className="space-y-4">
                <label className="flex items-center justify-between">
                  <span className="fa-body text-fa-gray-700">Show welcome message</span>
                  <input
                    type="checkbox"
                    checked={settings.showWelcomeMessage}
                    onChange={(e) => handleSettingsUpdate({ showWelcomeMessage: e.target.checked })}
                    className="fa-checkbox"
                  />
                </label>

                <label className="flex items-center justify-between">
                  <span className="fa-body text-fa-gray-700">Enable tooltips</span>
                  <input
                    type="checkbox"
                    checked={settings.enableTooltips}
                    onChange={(e) => handleSettingsUpdate({ enableTooltips: e.target.checked })}
                    className="fa-checkbox"
                  />
                </label>

                <label className="flex items-center justify-between">
                  <span className="fa-body text-fa-gray-700">Show keyboard shortcuts</span>
                  <input
                    type="checkbox"
                    checked={settings.showKeyboardShortcuts}
                    onChange={(e) => handleSettingsUpdate({ showKeyboardShortcuts: e.target.checked })}
                    className="fa-checkbox"
                  />
                </label>

                <label className="flex items-center justify-between">
                  <span className="fa-body text-fa-gray-700">Auto-start tour</span>
                  <input
                    type="checkbox"
                    checked={settings.autoStartTour}
                    onChange={(e) => handleSettingsUpdate({ autoStartTour: e.target.checked })}
                    className="fa-checkbox"
                  />
                </label>
              </div>

              <div className="flex gap-3 mt-6">
                <button
                  onClick={handleRestartTour}
                  className="fa-button-ghost px-4 py-2 rounded-lg font-medium flex items-center space-x-2 flex-1"
                >
                  <HelpCircle className="w-4 h-4" />
                  <span>Restart Tour</span>
                </button>
                <button
                  onClick={() => setShowSettings(false)}
                  className="fa-button-primary px-4 py-2 rounded-lg font-medium"
                >
                  Done
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Guided Tour */}
      <UserGuide
        steps={onboardingService.getWelcomeTourSteps()}
        isActive={showTour}
        onComplete={handleTourComplete}
        onSkip={handleTourSkip}
      />
    </div>
  );
};

// Hook for managing onboarding state
export const useOnboarding = () => {
  const [isFirstTime, setIsFirstTime] = useState(onboardingService.isFirstTimeUser());

  const startFeatureTour = (feature: string) => {
    const steps = onboardingService.getFeatureTourSteps(feature);
    if (steps.length > 0) {
      // This would trigger a feature-specific tour
      // Implementation depends on how you want to handle multiple tours
      console.log(`Starting ${feature} tour with ${steps.length} steps`);
    }
  };

  const markFeatureCompleted = (feature: string) => {
    onboardingService.markFeatureCompleted(feature);
  };

  const hasCompletedFeature = (feature: string) => {
    return onboardingService.hasCompletedFeature(feature);
  };

  return {
    isFirstTime,
    startFeatureTour,
    markFeatureCompleted,
    hasCompletedFeature,
    resetOnboarding: () => {
      onboardingService.resetOnboarding();
      setIsFirstTime(true);
    },
  };
};
