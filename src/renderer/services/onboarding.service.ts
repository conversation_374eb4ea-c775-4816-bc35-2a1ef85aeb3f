import { GuideStep } from '@renderer/components/ui/NotificationSystem';

export interface OnboardingState {
  isFirstTime: boolean;
  hasCompletedTour: boolean;
  currentStep: number;
  skippedSteps: string[];
  completedFeatures: string[];
  lastSeenVersion: string;
}

export interface OnboardingSettings {
  showWelcomeMessage: boolean;
  enableTooltips: boolean;
  showKeyboardShortcuts: boolean;
  autoStartTour: boolean;
}

class OnboardingService {
  private readonly STORAGE_KEY = 'fa-onboarding-state';
  private readonly SETTINGS_KEY = 'fa-onboarding-settings';
  private readonly CURRENT_VERSION = '1.0.0';

  private state: OnboardingState;
  private settings: OnboardingSettings;

  constructor() {
    this.state = this.loadState();
    this.settings = this.loadSettings();
  }

  private loadState(): OnboardingState {
    const stored = localStorage.getItem(this.STORAGE_KEY);
    if (stored) {
      try {
        return JSON.parse(stored);
      } catch (error) {
        console.warn('Failed to parse onboarding state:', error);
      }
    }

    return {
      isFirstTime: true,
      hasCompletedTour: false,
      currentStep: 0,
      skippedSteps: [],
      completedFeatures: [],
      lastSeenVersion: '',
    };
  }

  private loadSettings(): OnboardingSettings {
    const stored = localStorage.getItem(this.SETTINGS_KEY);
    if (stored) {
      try {
        return JSON.parse(stored);
      } catch (error) {
        console.warn('Failed to parse onboarding settings:', error);
      }
    }

    return {
      showWelcomeMessage: true,
      enableTooltips: true,
      showKeyboardShortcuts: true,
      autoStartTour: true,
    };
  }

  private saveState(): void {
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.state));
  }

  private saveSettings(): void {
    localStorage.setItem(this.SETTINGS_KEY, JSON.stringify(this.settings));
  }

  // Public API
  public isFirstTimeUser(): boolean {
    return this.state.isFirstTime;
  }

  public shouldShowWelcome(): boolean {
    return this.state.isFirstTime && this.settings.showWelcomeMessage;
  }

  public shouldAutoStartTour(): boolean {
    return this.state.isFirstTime && this.settings.autoStartTour && !this.state.hasCompletedTour;
  }

  public hasCompletedTour(): boolean {
    return this.state.hasCompletedTour;
  }

  public markTourCompleted(): void {
    this.state.hasCompletedTour = true;
    this.state.isFirstTime = false;
    this.state.lastSeenVersion = this.CURRENT_VERSION;
    this.saveState();
  }

  public markFeatureCompleted(feature: string): void {
    if (!this.state.completedFeatures.includes(feature)) {
      this.state.completedFeatures.push(feature);
      this.saveState();
    }
  }

  public hasCompletedFeature(feature: string): boolean {
    return this.state.completedFeatures.includes(feature);
  }

  public skipStep(stepId: string): void {
    if (!this.state.skippedSteps.includes(stepId)) {
      this.state.skippedSteps.push(stepId);
      this.saveState();
    }
  }

  public updateSettings(newSettings: Partial<OnboardingSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.saveSettings();
  }

  public getSettings(): OnboardingSettings {
    return { ...this.settings };
  }

  public resetOnboarding(): void {
    this.state = {
      isFirstTime: true,
      hasCompletedTour: false,
      currentStep: 0,
      skippedSteps: [],
      completedFeatures: [],
      lastSeenVersion: '',
    };
    this.saveState();
  }

  // Tour Steps Definition
  public getWelcomeTourSteps(): GuideStep[] {
    return [
      {
        id: 'welcome',
        target: 'body',
        title: '🎉 Welcome to Modern Todo!',
        content: 'Welcome to your new productivity companion! Let\'s take a quick tour to get you started with all the amazing features.',
      },
      {
        id: 'todo-input',
        target: '[data-tour="todo-input"]',
        title: '✍️ Create Your First Todo',
        content: 'Start by typing your first task here. You can add descriptions, set priorities, and assign due dates.',
        action: {
          label: 'Try it now',
          onClick: () => {
            const input = document.querySelector('[data-tour="todo-input"] input') as HTMLInputElement;
            if (input) {
              input.focus();
              input.placeholder = 'Try typing "Learn Modern Todo features"';
            }
          },
        },
      },
      {
        id: 'sidebar',
        target: '[data-tour="sidebar"]',
        title: '📂 Organize with Categories',
        content: 'Use the sidebar to organize your todos into categories like Work, Personal, or Learning. You can create custom categories too!',
      },
      {
        id: 'search',
        target: '[data-tour="search"]',
        title: '🔍 Powerful Search',
        content: 'Find any todo instantly with our advanced search. Search by title, description, tags, or even use filters for priority and status.',
      },
      {
        id: 'analytics',
        target: '[data-tour="analytics"]',
        title: '📊 Track Your Progress',
        content: 'View detailed analytics about your productivity, including completion rates, streaks, and trends over time.',
      },
      {
        id: 'sync',
        target: '[data-tour="sync"]',
        title: '☁️ Cloud Sync',
        content: 'Your data syncs automatically to the cloud with MotherDuck, so you never lose your todos and can access them anywhere.',
      },
      {
        id: 'keyboard-shortcuts',
        target: 'body',
        title: '⌨️ Keyboard Shortcuts',
        content: 'Speed up your workflow with keyboard shortcuts! Press Alt+H anytime to see all available shortcuts.',
        action: {
          label: 'Show shortcuts',
          onClick: () => {
            // Trigger keyboard shortcuts help
            window.dispatchEvent(new KeyboardEvent('keydown', { 
              key: 'h', 
              altKey: true,
              bubbles: true 
            }));
          },
        },
      },
      {
        id: 'accessibility',
        target: '[data-tour="accessibility"]',
        title: '♿ Accessibility Features',
        content: 'Modern Todo is built for everyone! Access accessibility settings to customize the experience for your needs.',
      },
      {
        id: 'complete',
        target: 'body',
        title: '🚀 You\'re All Set!',
        content: 'Congratulations! You\'re ready to boost your productivity. Remember, you can always access help and tutorials from the menu.',
      },
    ];
  }

  public getFeatureTourSteps(feature: string): GuideStep[] {
    switch (feature) {
      case 'search':
        return [
          {
            id: 'search-basic',
            target: '[data-tour="search"]',
            title: '🔍 Basic Search',
            content: 'Type any keyword to search through your todos. The search looks through titles, descriptions, and tags.',
          },
          {
            id: 'search-filters',
            target: '[data-tour="search-filters"]',
            title: '🎛️ Advanced Filters',
            content: 'Use filters to narrow down results by priority, status, category, or date range.',
          },
          {
            id: 'search-syntax',
            target: '[data-tour="search"]',
            title: '💡 Search Syntax',
            content: 'Try advanced search syntax like "priority:high" or "status:pending" for precise results.',
          },
        ];

      case 'analytics':
        return [
          {
            id: 'analytics-overview',
            target: '[data-tour="analytics"]',
            title: '📊 Analytics Overview',
            content: 'Get insights into your productivity with completion rates, trends, and goal tracking.',
          },
          {
            id: 'analytics-goals',
            target: '[data-tour="analytics-goals"]',
            title: '🎯 Set Goals',
            content: 'Set daily and weekly goals to stay motivated and track your progress.',
          },
          {
            id: 'analytics-insights',
            target: '[data-tour="analytics-insights"]',
            title: '💡 Smart Insights',
            content: 'Get personalized insights and suggestions to improve your productivity.',
          },
        ];

      case 'categories':
        return [
          {
            id: 'categories-create',
            target: '[data-tour="categories-create"]',
            title: '📂 Create Categories',
            content: 'Organize your todos by creating custom categories that fit your workflow.',
          },
          {
            id: 'categories-assign',
            target: '[data-tour="todo-input"]',
            title: '🏷️ Assign Categories',
            content: 'When creating or editing todos, you can assign them to categories for better organization.',
          },
          {
            id: 'categories-filter',
            target: '[data-tour="sidebar"]',
            title: '🔍 Filter by Category',
            content: 'Click on any category in the sidebar to view only todos from that category.',
          },
        ];

      default:
        return [];
    }
  }
}

export const onboardingService = new OnboardingService();
