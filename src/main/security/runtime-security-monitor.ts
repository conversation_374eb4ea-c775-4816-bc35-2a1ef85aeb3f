import { app, <PERSON><PERSON>erWindow } from 'electron';
import { EventEmitter } from 'events';
import { securityConfig } from './security-config';
import { auditLogger, AuditEventType, AuditSeverity } from './audit-logger';
import { intrusionDetection } from './intrusion-detection';

export interface SecurityThreat {
  id: string;
  type: SecurityThreatType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  source: string;
  timestamp: Date;
  metadata: any;
}

export enum SecurityThreatType {
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  MALICIOUS_CODE = 'malicious_code',
  DATA_BREACH_ATTEMPT = 'data_breach_attempt',
  PRIVILEGE_ESCALATION = 'privilege_escalation',
  INJECTION_ATTACK = 'injection_attack',
  BRUTE_FORCE = 'brute_force',
  ANOMALOUS_BEHAVIOR = 'anomalous_behavior'
}

export interface SecurityIncidentResponse {
  threatId: string;
  action: 'block' | 'monitor' | 'alert' | 'quarantine';
  automated: boolean;
  timestamp: Date;
}

export class RuntimeSecurityMonitor extends EventEmitter {
  private static instance: RuntimeSecurityMonitor;
  private threats: Map<string, SecurityThreat> = new Map();
  private responses: SecurityIncidentResponse[] = [];
  private monitoringActive: boolean = false;
  private securityMetrics: Map<string, number> = new Map();

  private constructor() {
    super();
    this.initializeMetrics();
  }

  public static getInstance(): RuntimeSecurityMonitor {
    if (!RuntimeSecurityMonitor.instance) {
      RuntimeSecurityMonitor.instance = new RuntimeSecurityMonitor();
    }
    return RuntimeSecurityMonitor.instance;
  }

  private initializeMetrics(): void {
    this.securityMetrics.set('threats_detected', 0);
    this.securityMetrics.set('incidents_blocked', 0);
    this.securityMetrics.set('suspicious_activities', 0);
    this.securityMetrics.set('failed_authentications', 0);
    this.securityMetrics.set('unauthorized_attempts', 0);
  }

  public startMonitoring(): void {
    if (this.monitoringActive) {
      return;
    }

    this.monitoringActive = true;
    this.log('Runtime security monitoring started', 'info');

    // Monitor Electron security events
    this.setupElectronMonitoring();

    // Monitor process security
    this.setupProcessMonitoring();

    // Monitor file system access
    this.setupFileSystemMonitoring();

    // Monitor network activity
    this.setupNetworkMonitoring();

    // Start periodic security checks
    this.startPeriodicChecks();
  }

  public stopMonitoring(): void {
    this.monitoringActive = false;
    this.log('Runtime security monitoring stopped', 'info');
  }

  private setupElectronMonitoring(): void {
    // Monitor web contents creation
    app.on('web-contents-created', (event, contents) => {
      this.monitorWebContents(contents);
    });

    // Monitor certificate errors
    app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
      this.handleCertificateError(url, error, certificate);
      callback(false); // Reject invalid certificates in production
    });

    // Monitor permission requests
    app.on('session-created', (session) => {
      session.setPermissionRequestHandler((webContents, permission, callback) => {
        this.handlePermissionRequest(webContents, permission, callback);
      });
    });
  }

  private monitorWebContents(contents: Electron.WebContents): void {
    // Monitor navigation attempts
    contents.on('will-navigate', (event, navigationUrl) => {
      if (!this.isNavigationAllowed(navigationUrl)) {
        event.preventDefault();
        this.reportThreat({
          type: SecurityThreatType.UNAUTHORIZED_ACCESS,
          severity: 'medium',
          description: `Blocked unauthorized navigation to: ${navigationUrl}`,
          source: 'web-contents',
          metadata: { url: navigationUrl }
        });
      }
    });

    // Monitor new window attempts
    contents.setWindowOpenHandler(({ url }) => {
      if (!this.isNavigationAllowed(url)) {
        this.reportThreat({
          type: SecurityThreatType.UNAUTHORIZED_ACCESS,
          severity: 'medium',
          description: `Blocked unauthorized window.open to: ${url}`,
          source: 'web-contents',
          metadata: { url }
        });
        return { action: 'deny' };
      }
      return { action: 'allow' };
    });

    // Monitor console messages for suspicious activity
    contents.on('console-message', (event, level, message, line, sourceId) => {
      this.analyzeConsoleMessage(level, message, sourceId);
    });
  }

  private setupProcessMonitoring(): void {
    // Monitor uncaught exceptions
    process.on('uncaughtException', (error) => {
      this.reportThreat({
        type: SecurityThreatType.ANOMALOUS_BEHAVIOR,
        severity: 'high',
        description: `Uncaught exception: ${error.message}`,
        source: 'process',
        metadata: { error: error.stack }
      });
    });

    // Monitor unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      this.reportThreat({
        type: SecurityThreatType.ANOMALOUS_BEHAVIOR,
        severity: 'medium',
        description: `Unhandled promise rejection: ${reason}`,
        source: 'process',
        metadata: { reason, promise }
      });
    });
  }

  private setupFileSystemMonitoring(): void {
    // Monitor file access patterns
    const fs = require('fs');
    const originalReadFile = fs.readFile;
    const originalWriteFile = fs.writeFile;

    fs.readFile = (...args: any[]) => {
      const filePath = args[0];
      this.analyzeFileAccess('read', filePath);
      return originalReadFile.apply(fs, args);
    };

    fs.writeFile = (...args: any[]) => {
      const filePath = args[0];
      this.analyzeFileAccess('write', filePath);
      return originalWriteFile.apply(fs, args);
    };
  }

  private setupNetworkMonitoring(): void {
    // Monitor network requests
    const { net } = require('electron');
    
    // This would be implemented with actual network monitoring
    // For now, we'll set up basic monitoring hooks
    this.log('Network monitoring initialized', 'info');
  }

  private startPeriodicChecks(): void {
    setInterval(() => {
      if (this.monitoringActive) {
        this.performSecurityHealthCheck();
      }
    }, 60000); // Check every minute
  }

  private isNavigationAllowed(url: string): boolean {
    const config = securityConfig.getSecurityConfig();
    const allowedOrigins = config.allowedOrigins;
    
    return allowedOrigins.some(origin => {
      if (origin.includes('*')) {
        const pattern = origin.replace(/\*/g, '.*');
        return new RegExp(pattern).test(url);
      }
      return url.startsWith(origin);
    });
  }

  private handleCertificateError(url: string, error: string, certificate: Electron.Certificate): void {
    this.reportThreat({
      type: SecurityThreatType.SUSPICIOUS_ACTIVITY,
      severity: 'high',
      description: `Certificate error for ${url}: ${error}`,
      source: 'certificate',
      metadata: { url, error, certificate }
    });
  }

  private handlePermissionRequest(
    webContents: Electron.WebContents,
    permission: string,
    callback: (permissionGranted: boolean) => void
  ): void {
    // Deny all permissions by default in production
    const allowedPermissions = ['notifications']; // Only allow specific permissions
    
    if (!allowedPermissions.includes(permission)) {
      this.reportThreat({
        type: SecurityThreatType.UNAUTHORIZED_ACCESS,
        severity: 'medium',
        description: `Blocked permission request: ${permission}`,
        source: 'permissions',
        metadata: { permission }
      });
      callback(false);
    } else {
      callback(true);
    }
  }

  private analyzeConsoleMessage(level: number, message: string, sourceId: string): void {
    // Look for suspicious console messages
    const suspiciousPatterns = [
      /eval\(/i,
      /document\.write/i,
      /innerHTML\s*=/i,
      /script\s*src/i,
      /javascript:/i,
      /data:text\/html/i
    ];

    if (suspiciousPatterns.some(pattern => pattern.test(message))) {
      this.reportThreat({
        type: SecurityThreatType.MALICIOUS_CODE,
        severity: 'high',
        description: `Suspicious console message detected: ${message}`,
        source: 'console',
        metadata: { level, message, sourceId }
      });
    }
  }

  private analyzeFileAccess(operation: 'read' | 'write', filePath: string): void {
    // Monitor access to sensitive files
    const sensitivePatterns = [
      /\.env/,
      /password/i,
      /secret/i,
      /key/i,
      /token/i,
      /config/i
    ];

    if (sensitivePatterns.some(pattern => pattern.test(filePath))) {
      this.reportThreat({
        type: SecurityThreatType.DATA_BREACH_ATTEMPT,
        severity: 'medium',
        description: `${operation} access to sensitive file: ${filePath}`,
        source: 'filesystem',
        metadata: { operation, filePath }
      });
    }
  }

  private performSecurityHealthCheck(): void {
    // Check for anomalous patterns
    const recentThreats = Array.from(this.threats.values())
      .filter(threat => Date.now() - threat.timestamp.getTime() < 300000); // Last 5 minutes

    if (recentThreats.length > 10) {
      this.reportThreat({
        type: SecurityThreatType.ANOMALOUS_BEHAVIOR,
        severity: 'critical',
        description: `High threat activity detected: ${recentThreats.length} threats in 5 minutes`,
        source: 'health-check',
        metadata: { threatCount: recentThreats.length }
      });
    }
  }

  public reportThreat(threatData: Omit<SecurityThreat, 'id' | 'timestamp'>): void {
    const threat: SecurityThreat = {
      id: this.generateThreatId(),
      timestamp: new Date(),
      ...threatData
    };

    this.threats.set(threat.id, threat);
    this.securityMetrics.set('threats_detected', 
      (this.securityMetrics.get('threats_detected') || 0) + 1);

    // Log the threat
    this.log(`Security threat detected: ${threat.description}`, 'warning');

    // Emit event for listeners
    this.emit('threat-detected', threat);

    // Trigger automated response
    this.triggerIncidentResponse(threat);

    // Log to audit system
    auditLogger.logSecurityEvent(
      threat.type as any,
      threat.description,
      AuditSeverity.HIGH,
      { threat }
    );
  }

  private triggerIncidentResponse(threat: SecurityThreat): void {
    let action: SecurityIncidentResponse['action'] = 'monitor';

    // Determine response based on threat severity and type
    if (threat.severity === 'critical') {
      action = 'block';
    } else if (threat.severity === 'high') {
      action = 'alert';
    }

    const response: SecurityIncidentResponse = {
      threatId: threat.id,
      action,
      automated: true,
      timestamp: new Date()
    };

    this.responses.push(response);
    this.securityMetrics.set('incidents_blocked',
      (this.securityMetrics.get('incidents_blocked') || 0) + 1);

    // Execute the response
    this.executeIncidentResponse(response, threat);
  }

  private executeIncidentResponse(response: SecurityIncidentResponse, threat: SecurityThreat): void {
    switch (response.action) {
      case 'block':
        this.log(`Blocking threat: ${threat.id}`, 'error');
        // Implement blocking logic
        break;
      case 'alert':
        this.log(`Alert triggered for threat: ${threat.id}`, 'warning');
        // Send alert to administrators
        break;
      case 'quarantine':
        this.log(`Quarantining threat: ${threat.id}`, 'warning');
        // Implement quarantine logic
        break;
      default:
        this.log(`Monitoring threat: ${threat.id}`, 'info');
    }
  }

  private generateThreatId(): string {
    return `threat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public getSecurityMetrics(): Map<string, number> {
    return new Map(this.securityMetrics);
  }

  public getActiveThreats(): SecurityThreat[] {
    const now = Date.now();
    return Array.from(this.threats.values())
      .filter(threat => now - threat.timestamp.getTime() < 3600000); // Last hour
  }

  private log(message: string, level: 'info' | 'warning' | 'error'): void {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [SECURITY] ${message}`);
  }
}

export const runtimeSecurityMonitor = RuntimeSecurityMonitor.getInstance();
