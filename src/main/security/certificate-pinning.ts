import { net } from 'electron';
import { securityConfig } from './security-config';

export interface CertificatePinConfig {
  pins: string[];
  enforceMode: boolean;
  reportUri?: string;
}

export class CertificatePinning {
  private static instance: CertificatePinning;
  private pinConfig: Map<string, CertificatePinConfig> = new Map();

  private constructor() {
    this.loadPinConfiguration();
  }

  public static getInstance(): CertificatePinning {
    if (!CertificatePinning.instance) {
      CertificatePinning.instance = new CertificatePinning();
    }
    return CertificatePinning.instance;
  }

  private loadPinConfiguration(): void {
    const config = {
    "api.motherduck.com": {
        "pins": [
            "sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=",
            "sha256/BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB="
        ],
        "enforceMode": true,
        "reportUri": "/security/cert-pin-report"
    }
};
    
    for (const [hostname, pinConfig] of Object.entries(config)) {
      this.pinConfig.set(hostname, pinConfig as CertificatePinConfig);
    }
  }

  public validateCertificate(hostname: string, certificate: Electron.Certificate): boolean {
    const pinConfig = this.pinConfig.get(hostname);
    if (!pinConfig) {
      return true; // No pinning configured for this host
    }

    const certFingerprint = this.getCertificateFingerprint(certificate);
    const isValid = pinConfig.pins.includes(certFingerprint);

    if (!isValid && pinConfig.enforceMode) {
      console.error(`Certificate pinning validation failed for ${hostname}`);
      this.reportPinningFailure(hostname, certFingerprint, pinConfig.reportUri);
      return false;
    }

    return true;
  }

  private getCertificateFingerprint(certificate: Electron.Certificate): string {
    // In a real implementation, this would extract the actual certificate fingerprint
    // For now, return a placeholder
    return 'sha256/' + Buffer.from(certificate.data).toString('base64');
  }

  private reportPinningFailure(hostname: string, fingerprint: string, reportUri?: string): void {
    const report = {
      hostname,
      fingerprint,
      timestamp: new Date().toISOString(),
      userAgent: 'Modern Todo App'
    };

    if (reportUri) {
      // Send report to security endpoint
      console.log('Certificate pinning failure reported:', report);
    }
  }
}

export const certificatePinning = CertificatePinning.getInstance();
