import { app, dialog } from 'electron';
import { autoUpdater } from 'electron-updater';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';
import { securityConfig } from './security-config';
import { auditLogger, AuditEventType, AuditSeverity } from './audit-logger';

export interface UpdateSignature {
  algorithm: string;
  signature: string;
  publicKey: string;
}

export interface SecureUpdateInfo {
  version: string;
  releaseDate: string;
  downloadUrl: string;
  signature: UpdateSignature;
  checksum: string;
  size: number;
  releaseNotes: string;
  critical: boolean;
}

export class SecureUpdater {
  private static instance: SecureUpdater;
  private updateInProgress: boolean = false;
  private publicKey: string;
  private updateServer: string;

  private constructor() {
    this.publicKey = this.loadPublicKey();
    this.updateServer = process.env.UPDATE_SERVER_URL || 'https://updates.moderntodo.app';
    this.setupAutoUpdater();
  }

  public static getInstance(): SecureUpdater {
    if (!SecureUpdater.instance) {
      SecureUpdater.instance = new SecureUpdater();
    }
    return SecureUpdater.instance;
  }

  private loadPublicKey(): string {
    // In production, this would load the actual public key
    // For now, return a placeholder
    return `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
-----END PUBLIC KEY-----`;
  }

  private setupAutoUpdater(): void {
    const config = securityConfig.getSecurityConfig();
    
    if (!config.enableAutoUpdates) {
      return;
    }

    // Configure auto-updater with security settings
    autoUpdater.setFeedURL({
      provider: 'generic',
      url: this.updateServer,
      requestHeaders: {
        'User-Agent': `ModernTodo/${app.getVersion()}`,
        'X-API-Key': process.env.UPDATE_API_KEY || ''
      }
    });

    // Set up event handlers
    autoUpdater.on('checking-for-update', () => {
      this.log('Checking for updates...', 'info');
    });

    autoUpdater.on('update-available', (info) => {
      this.handleUpdateAvailable(info);
    });

    autoUpdater.on('update-not-available', () => {
      this.log('No updates available', 'info');
    });

    autoUpdater.on('error', (error) => {
      this.handleUpdateError(error);
    });

    autoUpdater.on('download-progress', (progress) => {
      this.handleDownloadProgress(progress);
    });

    autoUpdater.on('update-downloaded', (info) => {
      this.handleUpdateDownloaded(info);
    });

    // Check for updates on startup (with delay)
    setTimeout(() => {
      this.checkForUpdates();
    }, 30000); // Wait 30 seconds after startup
  }

  public async checkForUpdates(): Promise<void> {
    if (this.updateInProgress) {
      this.log('Update already in progress', 'warning');
      return;
    }

    try {
      this.log('Checking for secure updates...', 'info');
      
      // Fetch update information securely
      const updateInfo = await this.fetchUpdateInfo();
      
      if (updateInfo) {
        // Verify update signature
        const isValid = await this.verifyUpdateSignature(updateInfo);
        
        if (!isValid) {
          throw new Error('Update signature verification failed');
        }

        // Check if update is newer than current version
        if (this.isNewerVersion(updateInfo.version)) {
          await this.initiateSecureUpdate(updateInfo);
        } else {
          this.log('Current version is up to date', 'info');
        }
      }

    } catch (error) {
      this.handleUpdateError(error);
    }
  }

  private async fetchUpdateInfo(): Promise<SecureUpdateInfo | null> {
    try {
      const response = await fetch(`${this.updateServer}/api/updates/latest`, {
        method: 'GET',
        headers: {
          'User-Agent': `ModernTodo/${app.getVersion()}`,
          'X-API-Key': process.env.UPDATE_API_KEY || '',
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Update server responded with ${response.status}`);
      }

      const updateInfo: SecureUpdateInfo = await response.json();
      return updateInfo;

    } catch (error) {
      this.log(`Failed to fetch update info: ${error.message}`, 'error');
      return null;
    }
  }

  private async verifyUpdateSignature(updateInfo: SecureUpdateInfo): Promise<boolean> {
    try {
      const { signature, algorithm } = updateInfo.signature;
      
      // Create verification data
      const verificationData = JSON.stringify({
        version: updateInfo.version,
        downloadUrl: updateInfo.downloadUrl,
        checksum: updateInfo.checksum,
        size: updateInfo.size
      });

      // Verify signature
      const verifier = crypto.createVerify(algorithm);
      verifier.update(verificationData);
      
      const isValid = verifier.verify(this.publicKey, signature, 'base64');
      
      if (isValid) {
        this.log('Update signature verified successfully', 'info');
      } else {
        this.log('Update signature verification failed', 'error');
        
        // Log security event
        await auditLogger.logSecurityEvent(
          'UPDATE_SIGNATURE_FAILURE' as AuditEventType,
          'Update signature verification failed',
          AuditSeverity.HIGH,
          { updateInfo }
        );
      }

      return isValid;

    } catch (error) {
      this.log(`Signature verification error: ${error.message}`, 'error');
      return false;
    }
  }

  private isNewerVersion(remoteVersion: string): boolean {
    const currentVersion = app.getVersion();
    
    // Simple version comparison (in production, use a proper semver library)
    const current = currentVersion.split('.').map(Number);
    const remote = remoteVersion.split('.').map(Number);
    
    for (let i = 0; i < Math.max(current.length, remote.length); i++) {
      const currentPart = current[i] || 0;
      const remotePart = remote[i] || 0;
      
      if (remotePart > currentPart) {
        return true;
      } else if (remotePart < currentPart) {
        return false;
      }
    }
    
    return false;
  }

  private async initiateSecureUpdate(updateInfo: SecureUpdateInfo): Promise<void> {
    this.updateInProgress = true;
    
    try {
      this.log(`Secure update available: ${updateInfo.version}`, 'info');
      
      // Log update initiation
      await auditLogger.logSecurityEvent(
        'UPDATE_INITIATED' as AuditEventType,
        `Secure update initiated for version ${updateInfo.version}`,
        AuditSeverity.MEDIUM,
        { updateInfo }
      );

      // Show update dialog to user
      const userChoice = await this.showUpdateDialog(updateInfo);
      
      if (userChoice) {
        // Download and verify update
        await this.downloadAndVerifyUpdate(updateInfo);
      } else {
        this.updateInProgress = false;
      }

    } catch (error) {
      this.updateInProgress = false;
      throw error;
    }
  }

  private async showUpdateDialog(updateInfo: SecureUpdateInfo): Promise<boolean> {
    const result = await dialog.showMessageBox({
      type: updateInfo.critical ? 'warning' : 'info',
      title: 'Secure Update Available',
      message: `A new version (${updateInfo.version}) is available.`,
      detail: `${updateInfo.releaseNotes}\n\nThis update has been cryptographically verified for security.`,
      buttons: ['Download and Install', 'Later'],
      defaultId: updateInfo.critical ? 0 : 1,
      cancelId: 1
    });

    return result.response === 0;
  }

  private async downloadAndVerifyUpdate(updateInfo: SecureUpdateInfo): Promise<void> {
    try {
      this.log('Downloading secure update...', 'info');
      
      // Use electron-updater for actual download
      autoUpdater.downloadUpdate();
      
    } catch (error) {
      this.log(`Update download failed: ${error.message}`, 'error');
      throw error;
    }
  }

  private async verifyDownloadedUpdate(filePath: string, expectedChecksum: string): Promise<boolean> {
    try {
      const fileBuffer = fs.readFileSync(filePath);
      const actualChecksum = crypto.createHash('sha256').update(fileBuffer).digest('hex');
      
      const isValid = actualChecksum === expectedChecksum;
      
      if (isValid) {
        this.log('Downloaded update checksum verified', 'info');
      } else {
        this.log('Downloaded update checksum verification failed', 'error');
        
        // Log security event
        await auditLogger.logSecurityEvent(
          'UPDATE_CHECKSUM_FAILURE' as AuditEventType,
          'Downloaded update checksum verification failed',
          AuditSeverity.HIGH,
          { expectedChecksum, actualChecksum }
        );
      }

      return isValid;

    } catch (error) {
      this.log(`Checksum verification error: ${error.message}`, 'error');
      return false;
    }
  }

  private handleUpdateAvailable(info: any): void {
    this.log(`Update available: ${info.version}`, 'info');
  }

  private handleUpdateError(error: Error): void {
    this.log(`Update error: ${error.message}`, 'error');
    this.updateInProgress = false;
    
    // Log security event for update errors
    auditLogger.logSecurityEvent(
      'UPDATE_ERROR' as AuditEventType,
      `Update process error: ${error.message}`,
      AuditSeverity.MEDIUM,
      { error: error.stack }
    );
  }

  private handleDownloadProgress(progress: any): void {
    const percent = Math.round(progress.percent);
    this.log(`Update download progress: ${percent}%`, 'info');
  }

  private async handleUpdateDownloaded(info: any): Promise<void> {
    this.log('Update downloaded successfully', 'info');
    
    // Log successful download
    await auditLogger.logSecurityEvent(
      'UPDATE_DOWNLOADED' as AuditEventType,
      `Update downloaded successfully: ${info.version}`,
      AuditSeverity.LOW,
      { info }
    );

    // Show restart dialog
    const result = await dialog.showMessageBox({
      type: 'info',
      title: 'Update Ready',
      message: 'Update has been downloaded and verified.',
      detail: 'The application will restart to apply the update.',
      buttons: ['Restart Now', 'Restart Later'],
      defaultId: 0
    });

    if (result.response === 0) {
      this.log('Applying secure update...', 'info');
      autoUpdater.quitAndInstall();
    }

    this.updateInProgress = false;
  }

  public async forceCheckForUpdates(): Promise<void> {
    if (this.updateInProgress) {
      throw new Error('Update already in progress');
    }

    await this.checkForUpdates();
  }

  public isUpdateInProgress(): boolean {
    return this.updateInProgress;
  }

  private log(message: string, level: 'info' | 'warning' | 'error'): void {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [SECURE-UPDATER] ${message}`);
  }
}

export const secureUpdater = SecureUpdater.getInstance();
