{"timestamp": "2025-08-17T22:43:14.107Z", "validation": {"errors": ["Missing required secure environment variable: SESSION_SECRET", "Missing required secure environment variable: ENCRYPTION_KEY", "Missing required secure environment variable: DATABASE_ENCRYPTION_KEY", "Missing required secure environment variable: JWT_SECRET"], "warnings": ["Development artifact in build: main/main/api/index.d.ts", "Development artifact in build: main/main/auth/auth.service.d.ts", "Development artifact in build: main/main/auth/crypto.service.d.ts", "Development artifact in build: main/main/auth/index.d.ts", "Development artifact in build: main/main/dao/base.dao.d.ts", "Development artifact in build: main/main/dao/category.dao.d.ts", "Development artifact in build: main/main/dao/index.d.ts", "Development artifact in build: main/main/dao/offline-first-base.dao.d.ts", "Development artifact in build: main/main/dao/todo.dao.d.ts", "Development artifact in build: main/main/dao/user.dao.d.ts", "Development artifact in build: main/main/database/connection.d.ts", "Development artifact in build: main/main/database/init.d.ts", "Development artifact in build: main/main/database/motherduck-data-examples.d.ts", "Development artifact in build: main/main/database/schema-initializer.d.ts", "Development artifact in build: main/main/database/schema.d.ts", "Development artifact in build: main/main/database/simple-motherduck-examples.d.ts", "Development artifact in build: main/main/database/test-auto-initialization.d.ts", "Development artifact in build: main/main/database/test-connection.d.ts", "Development artifact in build: main/main/database/test-crud.d.ts", "Development artifact in build: main/main/database/test-motherduck.d.ts", "Development artifact in build: main/main/database/test-params.d.ts", "Development artifact in build: main/main/index.d.ts", "Development artifact in build: main/main/mcp/comprehensive-status-check.d.ts", "Development artifact in build: main/main/mcp/service.d.ts", "Development artifact in build: main/main/mcp/test-connection.d.ts", "Development artifact in build: main/main/mcp/test-real-integration.d.ts", "Development artifact in build: main/main/mcp/test-with-mock-token.d.ts", "Development artifact in build: main/main/security/audit-logger.d.ts", "Development artifact in build: main/main/security/index.d.ts", "Development artifact in build: main/main/security/input-validator.d.ts", "Development artifact in build: main/main/security/intrusion-detection.d.ts", "Development artifact in build: main/main/security/ipc-security.d.ts", "Development artifact in build: main/main/security/rate-limiter.d.ts", "Development artifact in build: main/main/security/security-config.d.ts", "Development artifact in build: main/main/security/session-security.d.ts", "Development artifact in build: main/main/services/conflict-resolver.service.d.ts", "Development artifact in build: main/main/services/connection-pool.service.d.ts", "Development artifact in build: main/main/services/index.d.ts", "Development artifact in build: main/main/services/network-detector.service.d.ts", "Development artifact in build: main/main/services/offline-first-data.service.d.ts", "Development artifact in build: main/main/services/sync-engine.service.d.ts", "Development artifact in build: main/main/services/sync-manager.service.d.ts", "Development artifact in build: main/main/simple-auth-test.d.ts", "Development artifact in build: main/main/test-auth-verification.d.ts", "Development artifact in build: main/main/test-conflict-resolution.d.ts", "Development artifact in build: main/main/test-database.d.ts", "Development artifact in build: main/main/test-offline-first-strategy.d.ts", "Development artifact in build: main/main/test-sync-functionality.d.ts", "Development artifact in build: main/main/test-sync-status-monitoring.d.ts", "Development artifact in build: main/main/test-sync-with-conflicts.d.ts", "Development artifact in build: main/main/test-todo-crud.d.ts", "Development artifact in build: main/main/types/electron.d.ts", "Development artifact in build: main/main/types/index.d.ts", "Development artifact in build: main/main/utils/config.d.ts", "Development artifact in build: main/main/utils/environment.d.ts", "Development artifact in build: main/shared/types/electron.d.ts", "Development artifact in build: main/shared/types/errors.d.ts", "Development artifact in build: main/shared/types/index.d.ts", "Development artifact in build: main/shared/utils/validation.d.ts"], "info": ["Security implementation found: runtime-security-monitor.ts", "Security implementation found: secure-updater.ts", "Security implementation found: secure-backup.ts", "Security implementation found: certificate-pinning.ts", "Security implementation found: security-headers.ts", "Security-related packages found: 1"]}, "securityChecks": [{"name": "Environment Security", "passed": false, "details": "Secure environment variables and production settings"}, {"name": "Security Configuration", "passed": false, "details": "Security features and configuration validation"}, {"name": "Security Implementations", "passed": true, "details": "5/5 security implementations found"}, {"name": "Build Security", "passed": false, "details": "Build output security validation"}, {"name": "Certificates and Keys", "passed": true, "details": "No certificate files found"}, {"name": "Dependency Security", "passed": false, "details": "Dependency vulnerability and security package validation"}], "summary": {"totalChecks": 6, "passedChecks": 2, "totalErrors": 4, "totalWarnings": 59, "securityScore": 0, "productionReady": false}}