#!/usr/bin/env node

/**
 * Production Security Validation Script
 * Comprehensive security validation for production deployment
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class SecurityValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.info = [];
    this.securityChecks = [];
  }

  log(message, type = 'info') {
    const colors = {
      error: '\x1b[31m',
      warning: '\x1b[33m',
      info: '\x1b[36m',
      success: '\x1b[32m',
      reset: '\x1b[0m'
    };
    
    const icon = {
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️',
      success: '✅'
    };
    
    console.log(`${colors[type]}${icon[type]} ${message}${colors.reset}`);
  }

  validateEnvironmentSecurity() {
    this.log('Validating environment security', 'info');
    
    const requiredSecureVars = [
      'SESSION_SECRET',
      'ENCRYPTION_KEY',
      'DATABASE_ENCRYPTION_KEY',
      'JWT_SECRET'
    ];

    const envFiles = ['.env.production', '.env.production.secure'];
    let envFound = false;

    for (const envFile of envFiles) {
      if (fs.existsSync(envFile)) {
        envFound = true;
        const envContent = fs.readFileSync(envFile, 'utf8');
        
        // Check for required secure variables
        for (const varName of requiredSecureVars) {
          const regex = new RegExp(`^${varName}=(.+)$`, 'm');
          const match = envContent.match(regex);
          
          if (!match) {
            this.errors.push(`Missing required secure environment variable: ${varName}`);
          } else {
            const value = match[1];
            
            // Validate key strength
            if (varName.includes('KEY') || varName.includes('SECRET')) {
              if (value.length < 32) {
                this.errors.push(`Weak ${varName}: must be at least 32 characters`);
              } else if (!/^[a-fA-F0-9]+$/.test(value) && varName.includes('KEY')) {
                this.warnings.push(`${varName} should be hexadecimal for better security`);
              }
            }
          }
        }

        // Check for insecure settings
        const insecurePatterns = [
          { pattern: /ENABLE_DEVTOOLS=true/i, message: 'DevTools should be disabled in production' },
          { pattern: /EXPOSE_STACK_TRACES=true/i, message: 'Stack traces should not be exposed in production' },
          { pattern: /DETAILED_ERROR_MESSAGES=true/i, message: 'Detailed error messages should be disabled in production' },
          { pattern: /CSP_REPORT_ONLY=true/i, message: 'CSP should be enforced, not report-only in production' },
          { pattern: /ENABLE_SANDBOX=false/i, message: 'Sandbox should be enabled in production' }
        ];

        for (const { pattern, message } of insecurePatterns) {
          if (pattern.test(envContent)) {
            this.errors.push(message);
          }
        }
      }
    }

    if (!envFound) {
      this.errors.push('No production environment file found');
    }

    this.securityChecks.push({
      name: 'Environment Security',
      passed: this.errors.length === 0,
      details: 'Secure environment variables and production settings'
    });
  }

  validateSecurityConfiguration() {
    this.log('Validating security configuration', 'info');
    
    const securityConfigPath = 'src/main/security/security-config.ts';
    
    if (!fs.existsSync(securityConfigPath)) {
      this.errors.push('Security configuration file not found');
      return;
    }

    const configContent = fs.readFileSync(securityConfigPath, 'utf8');
    
    // Check for required security features
    const requiredFeatures = [
      'disableNodeIntegration',
      'enableContextIsolation',
      'enableAuditLogging',
      'enableSecurityHeaders',
      'enableCSRFProtection',
      'cspEnabled'
    ];

    for (const feature of requiredFeatures) {
      if (!configContent.includes(feature)) {
        this.warnings.push(`Security feature not found in config: ${feature}`);
      }
    }

    // Check for insecure defaults
    const insecurePatterns = [
      { pattern: /disableNodeIntegration:\s*false/i, message: 'Node integration should be disabled' },
      { pattern: /enableContextIsolation:\s*false/i, message: 'Context isolation should be enabled' },
      { pattern: /enableSandbox:\s*false/i, message: 'Sandbox should be enabled in production' }
    ];

    for (const { pattern, message } of insecurePatterns) {
      if (pattern.test(configContent)) {
        this.errors.push(message);
      }
    }

    this.securityChecks.push({
      name: 'Security Configuration',
      passed: this.errors.length === 0,
      details: 'Security features and configuration validation'
    });
  }

  validateSecurityImplementations() {
    this.log('Validating security implementations', 'info');
    
    const securityFiles = [
      'src/main/security/runtime-security-monitor.ts',
      'src/main/security/secure-updater.ts',
      'src/main/security/secure-backup.ts',
      'src/main/security/certificate-pinning.ts',
      'src/main/security/security-headers.ts'
    ];

    let implementedCount = 0;
    
    for (const file of securityFiles) {
      if (fs.existsSync(file)) {
        implementedCount++;
        this.info.push(`Security implementation found: ${path.basename(file)}`);
      } else {
        this.warnings.push(`Security implementation missing: ${path.basename(file)}`);
      }
    }

    if (implementedCount < securityFiles.length * 0.8) {
      this.errors.push('Insufficient security implementations for production');
    }

    this.securityChecks.push({
      name: 'Security Implementations',
      passed: implementedCount >= securityFiles.length * 0.8,
      details: `${implementedCount}/${securityFiles.length} security implementations found`
    });
  }

  validateBuildSecurity() {
    this.log('Validating build security', 'info');
    
    const distPath = 'dist';
    
    if (!fs.existsSync(distPath)) {
      this.errors.push('Build output not found. Run production build first.');
      return;
    }

    // Check for sensitive files in build
    const sensitivePatterns = [
      /\.env/,
      /\.key$/,
      /\.pem$/,
      /password/i,
      /secret/i,
      /private.*key/i
    ];

    this.scanForSensitiveFiles(distPath, sensitivePatterns);

    // Check for development artifacts
    const devArtifacts = [
      /\.map$/,
      /\.ts$/,
      /\.tsx$/,
      /\.test\./,
      /\.spec\./,
      /node_modules/
    ];

    this.scanForDevArtifacts(distPath, devArtifacts);

    this.securityChecks.push({
      name: 'Build Security',
      passed: this.errors.length === 0,
      details: 'Build output security validation'
    });
  }

  scanForSensitiveFiles(dir, patterns, relativePath = '') {
    if (!fs.existsSync(dir)) return;
    
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const relativeFilePath = path.join(relativePath, item);
      const stats = fs.statSync(fullPath);
      
      if (stats.isDirectory()) {
        this.scanForSensitiveFiles(fullPath, patterns, relativeFilePath);
      } else {
        patterns.forEach(pattern => {
          if (pattern.test(item)) {
            this.errors.push(`Sensitive file found in build: ${relativeFilePath}`);
          }
        });
      }
    });
  }

  scanForDevArtifacts(dir, patterns, relativePath = '') {
    if (!fs.existsSync(dir)) return;
    
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const relativeFilePath = path.join(relativePath, item);
      const stats = fs.statSync(fullPath);
      
      if (stats.isDirectory()) {
        this.scanForDevArtifacts(fullPath, patterns, relativeFilePath);
      } else {
        patterns.forEach(pattern => {
          if (pattern.test(item)) {
            this.warnings.push(`Development artifact in build: ${relativeFilePath}`);
          }
        });
      }
    });
  }

  validateCertificatesAndKeys() {
    this.log('Validating certificates and keys', 'info');
    
    // Check for certificate files
    const certPaths = [
      'certs/',
      'certificates/',
      'ssl/'
    ];

    let certsFound = false;
    
    for (const certPath of certPaths) {
      if (fs.existsSync(certPath)) {
        certsFound = true;
        const certFiles = fs.readdirSync(certPath);
        
        for (const file of certFiles) {
          if (file.endsWith('.pem') || file.endsWith('.crt') || file.endsWith('.key')) {
            this.info.push(`Certificate/key file found: ${file}`);
            
            // Check file permissions (Unix-like systems)
            try {
              const stats = fs.statSync(path.join(certPath, file));
              const mode = stats.mode & parseInt('777', 8);
              
              if (file.endsWith('.key') && mode > parseInt('600', 8)) {
                this.warnings.push(`Private key file has overly permissive permissions: ${file}`);
              }
            } catch (error) {
              // Ignore permission check errors on Windows
            }
          }
        }
      }
    }

    this.securityChecks.push({
      name: 'Certificates and Keys',
      passed: true, // This is informational
      details: certsFound ? 'Certificate files found and validated' : 'No certificate files found'
    });
  }

  validateDependencySecurity() {
    this.log('Validating dependency security', 'info');
    
    const packageJsonPath = 'package.json';
    
    if (!fs.existsSync(packageJsonPath)) {
      this.errors.push('package.json not found');
      return;
    }

    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Check for known vulnerable packages (simplified check)
    const knownVulnerable = [
      'lodash@4.17.15', // Example - in real implementation, use a vulnerability database
      'moment@2.24.0'
    ];

    const allDeps = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies
    };

    for (const [pkg, version] of Object.entries(allDeps)) {
      const pkgVersion = `${pkg}@${version}`;
      if (knownVulnerable.includes(pkgVersion)) {
        this.warnings.push(`Potentially vulnerable dependency: ${pkgVersion}`);
      }
    }

    // Check for security-related packages
    const securityPackages = [
      'helmet',
      'bcrypt',
      'crypto',
      'node-forge'
    ];

    let securityPkgCount = 0;
    for (const pkg of securityPackages) {
      if (allDeps[pkg]) {
        securityPkgCount++;
      }
    }

    this.info.push(`Security-related packages found: ${securityPkgCount}`);

    this.securityChecks.push({
      name: 'Dependency Security',
      passed: this.errors.length === 0,
      details: 'Dependency vulnerability and security package validation'
    });
  }

  generateSecurityReport() {
    const report = {
      timestamp: new Date().toISOString(),
      validation: {
        errors: this.errors,
        warnings: this.warnings,
        info: this.info
      },
      securityChecks: this.securityChecks,
      summary: {
        totalChecks: this.securityChecks.length,
        passedChecks: this.securityChecks.filter(check => check.passed).length,
        totalErrors: this.errors.length,
        totalWarnings: this.warnings.length,
        securityScore: this.calculateSecurityScore(),
        productionReady: this.errors.length === 0
      }
    };
    
    // Save report
    const reportPath = path.join('build', 'reports', `security-validation-${Date.now()}.json`);
    fs.mkdirSync(path.dirname(reportPath), { recursive: true });
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    return { report, reportPath };
  }

  calculateSecurityScore() {
    const totalChecks = this.securityChecks.length;
    const passedChecks = this.securityChecks.filter(check => check.passed).length;
    const errorPenalty = this.errors.length * 10;
    const warningPenalty = this.warnings.length * 2;
    
    const baseScore = totalChecks > 0 ? (passedChecks / totalChecks) * 100 : 0;
    const finalScore = Math.max(0, baseScore - errorPenalty - warningPenalty);
    
    return Math.round(finalScore);
  }

  displayResults() {
    console.log('\n' + '='.repeat(60));
    this.log('PRODUCTION SECURITY VALIDATION REPORT', 'info');
    console.log('='.repeat(60));
    
    // Display security checks
    if (this.securityChecks.length > 0) {
      this.log('\nSECURITY CHECKS:', 'info');
      this.securityChecks.forEach(check => {
        const status = check.passed ? '✅' : '❌';
        console.log(`  ${status} ${check.name}: ${check.details}`);
      });
    }
    
    if (this.info.length > 0) {
      this.log('\nINFORMATION:', 'info');
      this.info.forEach(msg => console.log(`  ${msg}`));
    }
    
    if (this.warnings.length > 0) {
      this.log('\nWARNINGS:', 'warning');
      this.warnings.forEach(msg => this.log(`  ${msg}`, 'warning'));
    }
    
    if (this.errors.length > 0) {
      this.log('\nERRORS:', 'error');
      this.errors.forEach(msg => this.log(`  ${msg}`, 'error'));
    }
    
    console.log('\n' + '='.repeat(60));
    
    const securityScore = this.calculateSecurityScore();
    this.log(`Security Score: ${securityScore}/100`, securityScore >= 80 ? 'success' : 'warning');
    
    if (this.errors.length === 0) {
      this.log('✅ PRODUCTION SECURITY VALIDATION PASSED', 'success');
    } else {
      this.log('❌ PRODUCTION SECURITY VALIDATION FAILED', 'error');
    }
    
    this.log(`Checks: ${this.securityChecks.filter(c => c.passed).length}/${this.securityChecks.length} passed, Errors: ${this.errors.length}, Warnings: ${this.warnings.length}`, 'info');
  }

  async validate() {
    try {
      this.log('🔒 Starting production security validation', 'info');
      
      this.validateEnvironmentSecurity();
      this.validateSecurityConfiguration();
      this.validateSecurityImplementations();
      this.validateBuildSecurity();
      this.validateCertificatesAndKeys();
      this.validateDependencySecurity();
      
      const { reportPath } = this.generateSecurityReport();
      this.displayResults();
      
      this.log(`\n📄 Security validation report saved: ${reportPath}`, 'info');
      
      return this.errors.length === 0;
      
    } catch (error) {
      this.log(`Security validation failed: ${error.message}`, 'error');
      return false;
    }
  }
}

// Run security validation
if (require.main === module) {
  const validator = new SecurityValidator();
  validator.validate().then(passed => {
    process.exit(passed ? 0 : 1);
  });
}

module.exports = SecurityValidator;
